@extends('include.app')
@section('content')
    <span class="d-none" id="current-menu" data-menu="menu-products"></span>
    <div class="card mb-4">
        <div class="card-header">
            <h4 class="flex-grow-1">
                {{ __('Products') }}
                <div class="text-muted"><small>{{ __('Search by name or sku') }}</small></div>
            </h4>

            <div>
                @if(checkPermission('create_product'))
                    <a href="{{ route('productsForm') }}" class="ml-md-auto my-1 btn btn-primary text-white">{{ __('Add Product') }}</a>
                @endif
                @if(checkPermission('export_product'))
                    <a href="" data-url="{{ route('productsExport') }}" id="{{ $moduleID }}Export" class="my-1 ml-1 btn btn-secondary text-white">{{ __('Export') }}</a>
                @endif
            </div>
        </div>
        <div class="card-body table-id" id="{{ $moduleID }}List" data-module="{{ $moduleID }}">
            <div class="form-row text-left justify-content-end mb-2 row-gap-7">
                @if(checkFeatureControl('filter', 'brand'))
                <div class="col-md-3">
                    <select name="fbrand" class="form-control select2 filter-form" data-width="100%">
                        <option value="">{{ __('All Brand') }}</option>
                        @foreach(userBrandList() as $key => $item)
                            <option value="{{ $key }}">{{ $item }}</option>
                        @endforeach
                    </select>
                </div>
                @endif
                @if(checkFeatureControl('filter', 'country'))
                <div class="col-md-3">
                    <select name="fcountry" class="form-control select2 filter-form" data-width="100%">
                        <option value="">{{ __('All Countries') }}</option>
                        @foreach(config('staticdata.country') as $key => $item)
                            <option value="{{ $key }}">{{ $item }}</option>
                        @endforeach
                    </select>
                </div>
                @endif
                <div class="col-md-3">
                    <select name="fproductcategory" class="form-control select2 filter-form" data-width="100%" onchange="getProductSubcategory(this.value)">
                        <option value="">{{ __('All Product Category') }}</option>
                        @foreach($productCategories as $item)
                            <option value="{{ $item->id }}">{{ $item->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="fproductsubcategory" class="form-control select2 filter-form" id="fproductsubcategory" data-width="100%">
                        <option value="">{{ __('All Product Subcategory') }}</option>
                    </select>
                </div>
                {{-- <div class="col-md-3">
                    <select name="fpricegroup" class="form-control select2 filter-form" data-width="100%">
                        <option value="">{{ __('All Price Groups') }}</option>
                        @foreach($price_group as $item)
                            <option value="{{ $item->id }}">{{ $item->name }}</option>
                        @endforeach
                    </select>
                </div> --}}
                <div class="col-md-3">
                    <select name="fstatus" class="form-control select2 filter-form" data-width="100%">
                        <option value="all">{{ __('All Statuses') }}</option>
                        <option value="1">{{ __('Enabled') }}</option>
                        <option value="0">{{ __('Disabled') }}</option>
                    </select>
                </div>
                {{-- <div class="col-md-3">
                    <select name="fsaleable" class="form-control select2 filter-form" data-width="100%">
                        <option value="all">{{ __('All Saleable Types') }}</option>
                        <option value="1">{{ __('Saleable') }}</option>
                        <option value="0">{{ __('Not Saleable') }}</option>
                    </select>
                </div> --}}
                {{-- <div class="col-md-3">
                    <select name="fvariant" class="form-control select2 filter-form" data-width="100%">
                        <option value="all">{{ __('All Types') }}</option>
                        <option value="1">{{ __('Not Variant') }}</option>
                        <option value="0">{{ __('Is Variant') }}</option>
                    </select>
                </div> --}}
            </div>
            @if($bulk_action)
                @include('include.selected-box', [
                    'bulk_action' => $bulk_action,
                    'datatable_list' => $moduleID.'List'
                ])
            @endif
            <div class="table-responsive">
                <table class="table table-striped word-wrap" data-url="{{ route('productsList') }}" data-delete="{{ route('productsDelete') }}" data-status="{{ route('productsStatusUpdate') }}">
                    <thead>
                        <tr>
                            @if($bulk_action)
                            <th width="40px">
                                <label class="custom-checkbox">
                                    <input type="checkbox" value="1" class="form-check-input checkall" data-datatable="{{ $moduleID }}List">
                                    <span></span>
                                </label>
                            </th>
                            @endif
                            <th style="min-width:180px" data-column="name" class="sortable">{{ __('Name') }}</th>
                            <th style="min-width:100px" data-column="sku" class="sortable">{{ __('SKU') }}</th>
                            <th style="min-width:100px" data-column="product_category_id" class="sortable">{{ __('Product Category') }}</th>
                            <th style="min-width:100px" data-column="product_subcategory_id" class="sortable">{{ __('Product Subcategory') }}</th>
                            <th style="min-width:100px" data-column="is_out_of_stock" class="sortable">{{ __('Out Of Stock?') }}</th>
                            <!-- <th style="min-width:150px">{{ __('Brand') }}</th>
                            <th style="min-width:150px">{{ __('Country') }}</th>
                            <th style="min-width:150px">{{ __('Price Group') }}</th> -->
                            <th style="min-width:100px" class="text-center">{{ __('Enabled') }}</th>
                            <th style="min-width:120px" data-column="created_at" class="sortable">{{ __('Created On') }}</th>
                            @if(checkPermission('edit_product') || checkPermission('create_product') || checkPermission('delete_product'))
                            <th width="100px" class="text-center">{{ __('Action') }}</th>
                            @endif
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
            <input type="hidden" name="sort_col" id="sort_col" value="{{ request()->sort_col }}">
            <input type="hidden" name="sort_by" id="sort_by" value="{{ request()->sort_by }}">
        </div>
    </div>
    <script>
        function getProductSubcategory (categoryId) {
            $.ajax({
                url: '{{ route('productsAjaxProductSubcategory') }}?category_id='+categoryId+'&filter=1',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    var optionList = response.data;
                    var optionSelect = $('#fproductsubcategory');
                    optionSelect.empty();
                    optionSelect.append(optionList);
                },
                error: (error) => {
                    console.log(JSON.stringify(error));
                },
            });
        }
    </script>
@endsection
