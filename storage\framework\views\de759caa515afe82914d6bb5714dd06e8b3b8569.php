
<?php $__env->startSection('content'); ?>
    <span class="d-none" id="current-menu" data-menu="menu-products"></span>
    

    <div class="tab-content tabs mb-4">
        <?php if(!$id): ?>
            <?php echo $__env->make('product-form-details', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php else: ?>
            <?php $__currentLoopData = $tab_list; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if($tab == $key): ?>
                    <div class="card tab-pane p-0 <?php echo e($tab == $key ? 'active' : ''); ?>">
                        <?php echo $__env->make('product-form-'.$key, \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>
    </div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('include.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\MBB_Backend\resources\views/product-form.blade.php ENDPATH**/ ?>