<form action="<?php echo e(route('productCategoryAddUpdate')); ?>" method="post" enctype="multipart/form-data" id="<?php echo e($moduleID); ?>Form" autocomplete="off">
    <?php echo csrf_field(); ?>
    <div class="row">
        <div class="form-group col-md-6">
            <label>
                <?php echo e(__('Name')); ?>

                <span class="text-danger">*</span>
            </label>
            <input type="text" name="name" id="editName" class="form-control" required>
        </div>
        <div class="form-group col-md-3">
            <label>
                <?php echo e(__('Enable')); ?>

            </label>
            <div class="d-flex align-items-center">
                <label class="switch mb-0">
                    <input type="checkbox" name="status" id="editStatus" value="1" class="enable" checked>
                    <span class="slider round"></span>
                </label>
                <label for="editStatus" class="mb-0 ml-1 cursor-pointer"><?php echo e(__('Yes')); ?></label>
            </div>
        </div>

        <div class="form-group col-md-3">
            <label>
                <?php echo e(__('Online Order')); ?>

            </label>
            <div class="d-flex align-items-center">
                <label class="switch mb-0">
                    <input type="checkbox" name="online_order" id="editOnlineOrder" value="1" class="enable" checked>
                    <span class="slider round"></span>
                </label>
                <label for="editOnlineOrder" class="mb-0 ml-1 cursor-pointer"><?php echo e(__('Yes')); ?></label>
            </div>
            <label for="editOnlineOrder" style="font-weight: 400; color: red"><?php echo e(__('*Disable if order from WhatsApp')); ?></label>
        </div>
    </div>


    <input type="hidden" name="id" id="editId">
    <input class="btn btn-primary btn-lg" type="submit" value=" <?php echo e(__('Submit')); ?>">
</form>
    <?php /**PATH C:\Users\<USER>\Documents\GitHub\MBB_Backend\resources\views/modals/product-category-add.blade.php ENDPATH**/ ?>