/*# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY3VzdG9tLmNzcyIsInNvdXJjZXMiOlsiY3VzdG9tLnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiJ9 */

/*# sourceMappingURL=custom.css.map */
body {
    overflow-x: hidden;
}

.loader {
    background: rgb(0,0,0,0.5);
}

.loader > span {
    border: 16px solid #f3f3f3;
    border-top: 16px solid #d2b585;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    animation: spin 2s linear infinite;
    position: fixed;
    top: 50%;
    left: 50%;
    margin-top: -50px;
    margin-left: -30px;
}

.nav-pills .nav-item .nav-link.active {
    background-color: #d2b585;
}

.main-sidebar {
    background: #211746;
}

.switch {
    position: relative;
    display: inline-block;
    width: 55px;
    height: 28px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.switch input:disabled + .slider
{
    cursor: not-allowed;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: 0.4s;
    transition: 0.4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    -webkit-transition: 0.4s;
    transition: 0.4s;
}

input:checked + .slider {
    background-color: #000;
}

input:focus + .slider {
    box-shadow: 0 0 1px #2515b6;
}

input:checked + .slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
}

.custom-checkbox
{
    margin: 0px;
    cursor: pointer;
}

.custom-checkbox .form-check-input
{
    transform: scale(1.2);
    margin: 0px;
    margin-top: -10px;
    cursor: pointer;
}

.custom-checkbox .form-check-input:disabled,
.custom-checkbox .form-check-input:disabled + span
{
    cursor: not-allowed;
}

/* Rounded sliders */
.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

.carousel-img {
    width: 300px;
    height: 100%;
    max-height: 410px;
    margin: 0 auto;
}

.image-size {
    width: inherit;
    height: inherit;
    max-width: -webkit-fill-available;
    max-height: -webkit-fill-available;
    height: 400px;
    object-fit: contain;
}
#image-place {
    height: 400px;
}

.carousel-control-next-icon,
.carousel-control-prev-icon {
    background-color: #6777ef !important;
    height: 40px;
    width: 40px;
    padding: 20px 20px;
    background-size: 20px;
    border-radius: 50%;
}

.card .card-header
{
    border-bottom-color: rgba(0,0,0,.1);
    padding-top: 13px;
}

.card .card-body
{
    padding-top: 15px;
}

.form-group .control-label, .form-group > label
{
    margin-bottom: 5px;
}

.form-checkbox
{
    width: 100%;
    cursor: pointer;
}

.form-checkbox > input
{
    margin-right: 7px;
    transform: scale(1.2);
}

/*** CUSTOM FILE UPLOAD ***/
.file-box
{
	display: table;
	width: 100%;
}

.file-box:hover
{
	cursor:pointer;
}

.file-text
{
	display: table-cell;
	vertical-align: middle;
	border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
    border-right: 0px;
}

.file-btn
{
	display: table-cell;
	vertical-align: middle;
	width:100px;
	text-align:right;
    border-radius: 0px !important;
	border-top-right-radius: 3px !important;
    border-bottom-right-radius: 3px !important;
}

.file-btn input[type=button]
{
	margin-top:0px;
	border:0px;
    border-radius: 0px;
    max-width: 100px;
    background: transparent;
    color: #FFF;
}

.file-hidden
{
	height:0px;
	width:0px;
	overflow:hidden;
}
/*** END CUSTOM FILE UPLOAD ***/

textarea.form-control
{
    min-height: 120px;
    resize: none;
}

.image-box
{
    display: inline-block;
    border: 1px solid rgba(0,0,0,.1);
    border-radius: 5px;
    padding: 10px;
    margin-top:10px;
}

.image-box > img
{
    max-width: 100px;
}

.admin-logo
{
    max-height: 60px;
    max-width: 90%;
    margin-top: 5px;
}

.main-sidebar .sidebar-brand a
{
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.sidebar-mini .admin-logo
{
    max-width: 60px;
}

.main-sidebar .sidebar-menu li.menu-header
{
    margin-bottom: 0px !important;
    padding-bottom: 0px;
    line-height: 1.5;
    padding-top: 15px;
    padding-left: 0px;
    padding-right: 0px;
}

.main-sidebar .sidebar-menu li a
{
    padding: 0px 10px;
    height: 42px;
}

.main-sidebar .sidebar-menu li a i,
.main-sidebar a:not(.btn-social-icon):not(.btn-social):not(.page-link) .fas,
.main-sidebar a:not(.btn-social-icon):not(.btn-social):not(.page-link) .far
{
    margin-left: 0px;
    margin-right: 5px;
    margin-top: -1px;
}

label.checkbox-label
{
    display: flex;
    align-items: center;
    font-weight: normal;
    cursor: pointer;
}

label.checkbox-label > input
{
    transform: scale(1.2);
    margin-right: 10px;
    margin-left: 5px;
}

.toggle-password-box
{
    position: relative;
}

.toggle-password-box > input
{
    padding-right: 35px;
}

.toggle-password
{
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-45%);
    cursor: pointer;
}

.toggle-password > i
{
    color: #999;
    font-size: 15px;
}

.card .card-header,
.card .card-header h4,
.btn,
p
{
    line-height: 1.5;
}

.card .card-header .btn
{
    padding: 5px 15px;
}

.badge-sm
{
    font-size: 11px;
}

.badge
{
    line-height: 1.3;
}

.badge-mini {
    padding: 3px 4px;
    border-radius: 5px;
    line-height: 1.2;
    font-size: 11px;
}

.detail-box label
{
    margin-bottom: 2px;
    color: #888;
    font-weight: 500;
}

.detail-box label + div
{
    color: #000;
    font-size: 13px;
}

.cursor-pointer
{
    cursor: pointer;
}

.form-control:disabled
{
    cursor: not-allowed;
}

.form-control[readonly]
{
    background-color: #fdfdff;
}

/* select2 */
.select2.select2-container
{
    max-width: 100%;
}

.select2.select2-container--default .select2-selection--single
{
    border-color: #e4e6fc;
    background-color: #fdfdff;
}

.select2.select2-container.select2-container--focus .select2-selection--multiple,
.select2.select2-container.select2-container--focus .select2-selection--single
{
    border-color: #aaa;
}

.select2.select2-container--default .select2-selection--single .select2-selection__rendered
{
    line-height: 1.5;
    padding-top: 10px;
    padding-left: 15px;
    color: #495057;
}

.select2-container--default .select2-search--dropdown .select2-search__field
{
    padding-left: 10px;
    padding-right: 10px;
}

.select2-results .select2-results__option
{
    padding: 7px 15px;
    line-height: 1.5;
    color: #495057;
}

.select2-container--default .select2-results .select2-results__option[aria-selected=true]
{
    color: #495057;
}

.select2-container--default .select2-results .select2-results__option--highlighted[aria-selected]
{
    color: #FFF;
}

.dataTables_length
{
    margin-bottom: 5px;
}

.dataTables_length .select2-container
{
    margin: 0px 7px;
    min-width: 70px;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #666 !important;
}

/* multiple */
.select2.select2-container--default .select2-search--inline .select2-search__field
{
    width: auto !important;
    min-height: 29px;
    padding-top: 4px;
}

.select2.select2-container--default.select2-container--focus .select2-selection--multiple
{
    min-height: 42px;
}

.select2.select2-container--default .select2-selection--multiple
{
    border-color: #e4e6fc;
}

.select2.select2-container--default .select2-selection--multiple .select2-selection__rendered
{
    height: 100%;
    line-height: 1.5;
    padding: 0px 12px;
}

.select2.select2-container--default .select2-selection--multiple .select2-selection__choice
{
    margin-top: 8px;
    margin-right: 7px;
    color: #000;
    font-size: 13px;
    padding: 2px 0px 1px 7px;
}

.select2.select2-container--default .select2-selection--multiple .select2-selection__choice__remove
{
    float: right;
    padding: 0px 2px;
    font-size: 20px;
    font-weight: 500;
    line-height: 1;
    margin-left: 5px;
}
/* END multiple */
/* END select2 */

/* modal */
.modal-header
{
    border-bottom: 1px solid rgba(0,0,0,.1);
    padding-top: 20px;
    padding-bottom: 10px;
}

.modal-header .close
{
    font-weight: 500;
    font-size: 36px;
    padding: 9px;
}
/* END modal */

/* view tab box */
.tab-filter-box
{
    background-color: #444;
    margin-bottom: 15px;
}

.tab-filter-box .card .card-header
{
    border-bottom: 0px;
}

.tab-filter-box .form-group > label
{
    color: #FFF;
}

.tab-box h5
{
    display: block;
    color: #000;
    font-weight: 500;
    margin-bottom: 25px;
    border-bottom: 1px solid rgba(0, 0, 0, .1);
    padding-bottom: 15px;
}
/* END view tab box */

/* alert */
.alert ul
{
    line-height: 1.5;
    margin: 0px;
    padding: 0px;
    padding-left: 20px;
}
/* END alert */

/* doctors */
.user-img
{
    max-width: 100px;
    max-height: 100px;
    margin-right: 10px;
    border: 1px solid #000;
    border-radius: 5px;
}

.user-img-sm
{
    max-width: 60px;
    max-height: 60px;
}

.doctor-no
{
    display: block;
    color: #888;
    font-weight: 500;
}

/* slots */
ul.slot-box
{
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    margin:0px;
    padding: 0px;
}

ul.slot-box > li
{
    display: flex;
    width: 200px;
    line-height: 1.5;
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 5px;
    color: #000;
    margin-right: 10px;
    margin-bottom: 10px;
    padding: 10px 15px 7px 15px;
}

ul.slot-box > li > div > span
{
    display: block;
    font-size: 13px;
    color: #aaa;
}

ul.slot-box .slot-delete
{
    cursor: pointer;
    color: #f94141;
    font-size: 16px;
    display: flex;
    height: 18px;
    margin-top: 2px;
}
/* END slots */
/* END doctors */

.dashboard-content-box > p
{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
    margin-bottom: 0;
}

.text-grey
{
    color: grey;
}

.grey-box
{
    background-color: rgb(245, 245, 245);
    border-radius: 10px;
}

.swal-title
{
    font-size: 22px;
}

.pl-10
{
    padding-left: 10px !important;
}

/* action button */
.btn-icon
{
    display: inline-block;
    width: 28px;
    height: 28px;
    padding: 4px 8px;
    border-radius: 5px;
    margin: 3px 0px;
    border: 0.5px solid;
    text-align: center;
    box-shadow: none;
}

a.btn-icon > i
{
    margin-left: 0px !important;
    font-size: 12px;
}

a.btn-icon:hover
{
    color: #FFF;
}

a.btn-info:hover {
    color: #FFF !important;
}

/* Action buttons layout for larger screens */
@media (min-width: 1275px) {
    .action-buttons-container {
        display: flex !important;
        flex-wrap: nowrap !important;
        justify-content: center;
        align-items: center;
        gap: 3px;
        min-width: 110px;
        white-space: nowrap;
    }

    .action-buttons-container .btn-icon {
        margin: 0 !important;
        flex-shrink: 0;
        width: 28px;
        height: 28px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
}

/* Action buttons layout for smaller screens */
@media (max-width: 1274px) {
    .action-buttons-container {
        display: flex !important;
        flex-wrap: wrap !important;
        justify-content: center;
        align-items: center;
        gap: 2px;
    }

    .action-buttons-container .btn-icon {
        margin: 2px 1px !important;
    }
}
/* END action button */

/* data table */
.table:not(.table-sm) thead th
{
    font-weight: 500;
    color: #000;
    padding-top: 7px;
    padding-bottom: 7px;
}

table.dataTable th, table.dataTable td
{
    padding-top: 10px;
    padding-bottom: 10px;
    line-height: 1.5;
}

.sortable {
  cursor: pointer;
  position: relative;
}

.sortable::after {
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  content: "\f0dc";
  position: absolute;
  top: 50%;
  font-size: 16px;
  transform: translateY(-50%);
  margin-left: 5px;
}

.sortable.sorting_desc::after {
  content: "\f0dd";
  top: 42%;
}

.sortable.sorting_asc::after {
  content: "\f0de";
  top: 62%;
}

.dataTables_info
{
    display: inline-block;
    margin-bottom: 5px;
}

.dataTables_paginate
{
    display: inline-block;
    float: right;
}

.data-filter
{
    display: flex;
    align-items: center;
}

.data-filter > .dataTables_length
{
    margin-bottom: 0px;
    flex-grow: 1;
}

.dataTables_filter label input
{
    min-height: 42px;
}

div.dataTables_wrapper div.dataTables_processing {
    position: fixed;
    background-image: none !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgb(0,0,0,0.5);
    z-index: 9999;
}

div.dataTables_wrapper div.dataTables_processing::after {
    content:"";
    border: 16px solid #f3f3f3;
    border-top: 16px solid #d2b585;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    animation: spin 2s linear infinite;
    position: fixed;
    top: 50%;
    left: 50%;
    margin-top: -50px;
    margin-left: -30px;
}
/* END data table */

.form-outline
{
    position: relative;
}

.form-outline label.form-label
{
    position: absolute;
    top: -9px;
    left: 5px;
    background: #FFF;
    padding: 0px 5px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.4);
    margin-bottom: 0px;
    line-height: 1.5;
}

.form-outline .select2.select2-container--default
{
    width: 100% !important;
}

.form-outline .select2.select2-container--default .select2-selection--single .select2-selection__rendered
{
    padding-top: 12px;
}

.selected-box
{
    margin-top: 10px;
}

.selected-box .bulk-action
{
    width:200px;
}

.nav-pills
{
    margin: 5px 0px;
}

.nav-pills .nav-item .nav-link
{
    line-height: 1.5;
}

.fw-500
{
    font-weight: 500;
}

.fw-600
{
    font-weight: 600;
}

.font-size-12
{
    font-size: 12px;
}

.total-table > div
{
    background-color: whitesmoke;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 15px;
    border-bottom: 1px solid #c2c2c2;
    color: #000;
    font-weight: 500;
}

.total-table > div:last-child
{
    border-bottom: none;
    background-color: #363636;
    color: white;
}

.prescription-box
{
    background-color: whitesmoke;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 20px;
    border-bottom: 1px solid #c2c2c2;
}

.prescription-box:last-child
{
    border-bottom: none;
}

.rate-disabled
{
    color: rgb(184, 184, 184) !important;
}

.rate-active
{
    color: #f1c40f !important;
}

/* sidebar */
.main-sidebar .sidebar-menu li a i,
.main-sidebar .sidebar-menu li.menu-header,
.main-sidebar .sidebar-menu li a span {
    color: #FFF;
}

.main-sidebar .has-sub > a
{
    cursor: pointer;
}

.main-sidebar .has-sub a.selected,
.main-sidebar .sidebar-menu li a.selected i,
.main-sidebar .sidebar-menu li a.selected span
{
    color: #d2b585;
}

.main-sidebar .has-sub a.selected:hover
{
    background-color: transparent !important;
    color: #d2b585 !important;
}

.main-sidebar .has-sub a.selected:hover > i,
.main-sidebar .has-sub a.selected:hover > span
{
    color: #d2b585 !important;
}

.main-sidebar .has-sub > a::after {
    content: '\f078';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    float: right;
    color: #FFF;
}

.main-sidebar .has-sub.activeLi > a::after
{
    color: #FFF;
}

.main-sidebar .has-sub.activeLi
{
    background-color: transparent !important;
}

.main-sidebar .has-sub.activeLi > a,
.main-sidebar .has-sub.activeLi > a > i,
.main-sidebar .has-sub.activeLi > a > span,
.main-sidebar .has-sub.activeLi > a:after,
.main-sidebar .has-sub.activeLi > a:hover
{
    color: #d2b585 !important;
}

.main-sidebar .has-sub.activeLi > a:hover > i,
.main-sidebar .has-sub.activeLi > a:hover > span
{
    color: #d2b585 !important;
}

.main-sidebar ul.sub-nav
{
    padding:0px;
    margin-top: -2px;
}

.main-sidebar ul.sub-nav > li {
    margin: 0px !important;
}

.main-sidebar ul.sub-nav > li > a {
    height: auto;
    font-size: 13px;
    padding: 2px 5px 2px 36px;
    border: 0px;
    line-height: 1.5;
    font-weight: normal;
    color: #FFF;
}

.main-sidebar ul.sub-nav > li > a:before {
    content: "•";
    margin-right: 5px;
    font-size: 20px;
}

.main-sidebar ul.sub-nav > li > a.sub-selected,
.main-sidebar ul.sub-nav > li > a:hover {
    border-radius: 0px;
    color: #d2b585 !important;
}

.main-sidebar ul.sub-nav a > div.badge
{
    padding: 3px 7px;
    font-size: 11px;
    border-radius: 5px;
    margin-top: 0px;
}

.sidebar-mini .main-sidebar .sidebar-menu > li
{
    padding:0px;
}

.sidebar-mini .sidebar-menu li a:hover
{
    background-color: transparent !important;
}

.sidebar-mini .sidebar-menu li a i
{
    margin-right: 0px !important;
}

.sidebar-mini .sidebar-menu li a:hover i,
.sidebar-mini .sidebar-menu li a:hover::after
{
    color: #60686f !important;
}

.sidebar-mini .main-sidebar .has-sub
{
    position: relative;
}

.sidebar-mini .main-sidebar .has-sub > a::after
{
    font-size: 11px;
    margin-left: 2px;
    margin-right: 2px;
}

.sidebar-mini .main-sidebar .has-sub ul.sub-nav
{
    position: absolute;
    background: #211746;
    top: 10px;
    left: 60px;
    padding-left: 0px;
    margin-left: 0px;
    border-radius: 5px;
    min-width: 250px;
    padding-top: 5px;
    padding-bottom: 3px;
}

.sidebar-mini .main-sidebar .has-sub.activeLi ul.sub-nav
{
    top: -5px;
}

.sidebar-mini .main-sidebar .has-sub ul.sub-nav a
{
    color: #FFF;
    padding-left: 15px;
    padding-right: 15px;
    border: 0px;
    border-radius: 0px;
}

.sidebar-mini .main-sidebar .has-sub ul.sub-nav a:hover
{
    border-radius: 0px !important;
}
/* END sidebar */

img.border
{
    border: 1px solid #CCC;
    padding: 5px;
    border-radius: 5px;
    max-width: 80px;
    max-height: 80px;
}

.selected-data
{
    display: inline-block;
    background-color: #e4e4e4;
    border: 1px solid #aaa;
    border-radius: 4px;
    color: #000;
    font-size: 13px;
    padding: 3px 7px;
    margin-right: 5px;
    margin-bottom: 5px;
}

/* permissions */
.permissions-box
{
    border: 1px solid #e4e6fc;
    border-radius: .25rem;
    padding: 10px 13px;
    margin-bottom: 15px;
}

.permissions-box ul
{
    display: flex;
    list-style: none;
    padding-left: 0px;
    margin-bottom: 0px;
    flex-wrap: wrap;
    column-gap: 50px;
    row-gap: 3px;
    font-size: 13px;
}

.permissions-box ul li
{
    min-width: 200px;
}

label.custom-checkbox.same-line
{
    display: flex;
    align-items: center;
    font-weight: normal;
    letter-spacing: normal;
    width: fit-content;
}

label.custom-checkbox.same-line .form-check-input
{
    position: relative;
    margin-top: -2px;
    margin-left: 5px;
}

label.custom-checkbox.same-line > span
{
    margin-left: 10px;
}
/* END permissions */

/* notification */
.notification-icon > a
{
    position: relative;
    display: inline-block;
}

.notification-icon > a:active,
.notification-icon > a:focus,
.notification-icon > a:hover
{
    outline:0;
    text-decoration: none;
    color: #34395e;
}

.notification-icon > a > span
{
    position: absolute;
    top: -7px;
    right: -7px;
    padding: 5px 2px 5px 2px;
    border-radius: 100%;
    font-size: 10px;
    color: #FFF;
    min-width: 18px;
    max-height: 18px;
    text-align: center;
}

.notification-icon .dropdown-menu
{
    width: 350px;
    font-size: 14px;
    padding:0px;
}

.notification-icon .dropdown-menu a,
.notification-box a
{
    font-size: 13px;
    color: #34395e;
    border-bottom: 1px solid #DDD;
    display: block;
    padding: 12px 15px;
    text-decoration: none;
    font-weight: 400;
}

.notification-icon .dropdown-menu a:hover,
.notification-box a:hover
{
    background-color: #f5f5f5;
    color: #34395e;
}

.notification-icon .notify-title,
.notification-box .notify-title
{
    font-size: 14px;
    line-height: 1.3;
    margin-bottom: 3px;
    font-weight: 600;
}

.notification-icon .notify-content,
.notification-box .notify-content
{
    margin-bottom: 0px;
}

.notification-icon .notify-new,
.notification-box .notify-new
{
    color: #FFF;
    font-size: 11px;
    padding: 4px 7px;
    border-radius: 5px;
    margin-top: 7px;
    display: inline-block;
}

.notification-icon .notify-date,
.notification-box .notify-date
{
    color: #999;
    padding: 0px;
    margin:0px;
    font-size: 12px;
}

.notification-all > a
{
    text-align: center;
    border-bottom: 0px;
}

.notification-box table.dataTable
{
    margin-bottom: 0px;
    border-bottom: 0px !important;
}

.notification-box table.dataTable td
{
    padding: 0px;
}

.notification-box table.dataTable td.dataTables_empty
{
    padding: 15px 25px;
}

.notification-box .dataTables_info
{
    padding-left: 25px;
    margin-top: 15px;
    margin-bottom: 15px;
}

.notification-box .dataTables_paginate
{
    padding-right: 25px;
    margin-top: 15px;
}

.notification-box td a
{
    padding: 12px 25px;
}
/* END notification */

/* assessment */
ul.assessment-test-box
{
    list-style: none;
    padding-left: 0px;
    margin:0px;
}

.assessment-test-search
{
    position: relative;
}

.assessment-test-search i
{
    position: absolute;
    top: 52%;
    transform: translateY(-50%);
    left: 10px;
}

.assessment-test-search input.form-control
{
    padding-left: 30px !important;
}

ul.assessment-test-box a
{
    color: #34395e;
    font-weight: 400;
    text-decoration: none;
    display: block;
}

ul.assessment-test-box > li:not(:last-child) > a
{
    margin-bottom: 5px;
}

ul.assessment-test-box a.has-subgroup::after
{
    content: '\f078';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-left:2px;
    font-size: 11px;
}

ul.assessment-test-box > li:not(:first-child)
{
    padding-left: 2px;
}

ul.assessment-test-box ul
{
    list-style: none;
    padding-left: 25px;
    margin-bottom: 7px;
    margin-top: -5px;
}

.assessment-content-box .grey-box
{
    border-radius: 0px;
    border: 1px solid #DDD;
    margin-left: 30px;
}

.assessment-content-box textarea.form-control
{
    min-height: 80px;
}

ul.assessment-test-box a.assessment-menu-active,
ul.assessment-test-box a.assessment-menu-selected
{
    color: #43b28a;
    font-weight: 500;
}

ul.assessment-test-box a.assessment-menu-selected
{
    display: flex;
    align-items: center;
}

ul.assessment-test-box a.assessment-menu-selected > i
{
    background: #43b28a;
    color: #FFF;
    border-radius: 100%;
    padding: 3px;
    font-size: 10px;
}
/* END assessment */

/* assessment spectacles */
table.assessment-spectacles .bg-light
{
    background-color: #EEE !important;
}

table.assessment-spectacles thead td
{
    height: unset !important;
    padding: 10px !important;
    font-weight: 500;
}

table.assessment-spectacles td
{
    padding: 7px 10px !important;
    border: 1px solid #DDD;
    height: auto !important;
}

table.assessment-spectacles.valign-top td
{
    vertical-align: top !important;

}

table.assessment-spectacles td.no-border
{
    border-top: 0px;
    border-left: 0px;
    border-right: 0px;
}

table.assessment-spectacles .w-200
{
    width: 200px;
}

table.assessment-spectacles input.form-control
{
    padding: 10px !important;
}

table.assessment-spectacles .select2.select2-container--default .select2-selection--single .select2-selection__rendered
{
    padding-left: 10px;
}
/* END assessment spectacles */

/* assessment tab */
ul.assessment-tab
{
    list-style: none;
    padding-left: 0px;
    display: flex;
    flex-wrap: wrap;
    column-gap: 7px;
    row-gap: 15px;
    margin-bottom: 25px;
}

ul.assessment-tab li > a
{
    border: 1px solid #DDD;
    padding: 7px 15px;
    border-radius: 5px;
    color: #34395e;
    font-weight: 400;
    text-decoration: none;
}

ul.assessment-tab li > a:hover,
ul.assessment-tab li > a.active
{
    background: #d2b585;
    border-color: #d2b585;
    color: #FFF;
}
/* END assessment tab */

.text-dark-gray
{
    color: #34395e;
    font-weight: 500;
}

.password-box
{
    position: relative;
}

.password-box input
{
    padding-right: 35px !important;
}

.btn-toggle-password
{
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
}

.image-preview-box
{
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.image-preview-box > a
{
    position: relative;
    width: 100px;
    height: 100px;
    border: 1px solid #DDD;
    border-radius: 5px;
    padding: 7px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.image-preview-box > a > img
{
    max-width: 100%;
    max-height: 100%;
}

.image-preview-box > a > i
{
    font-size: 50px;
    color: #aaa;
}

.image-preview-box .image-delete
{
    position: absolute;
    right: 2px;
    top: 3px;
    font-size: 20px;
    /* color: red; */
    background: #FFF;
    border-radius: 100%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-preview-box .image-delete > i
{
    color: red;
}

/* change color */
.theme-white .nav-pills .nav-link.active,
.theme-white .btn-primary,
.theme-white .btn-primary:not(:disabled):not(.disabled):active,
.btn-primary, .btn-primary.disabled,
.theme-white .bg-primary,
.theme-white .swal-button.swal-button--confirm,
.theme-white .btn-primary:focus
{
    background-color: #d2b585 !important;
}

.theme-white .btn-primary:not(:disabled):not(.disabled):active,
.btn-primary, .btn-primary.disabled {
    border:0px !important;
}

.theme-white .btn-primary:hover
{
    background-color: #d2b585 !important;
    opacity: 0.8;
}

body,
.nav-pills .nav-item .nav-link,
.form-group .control-label, .form-group > label,
.file-btn input[type=button],
.theme-white .page-item .page-link,
.theme-white .page-item.disabled .page-link,
.table,
.form-control
{
    color: #000;
}

.theme-white .form-control:focus {
    border-color: #000;
}

.btn.file-btn:not(.btn-social):not(.btn-social-icon):hover input[type=button],
a.btn-icon:hover
{
    color: #FFF !important;
}

.dark,
.light-sidebar.sidebar-mini .main-sidebar .sidebar-menu,
.main-sidebar .sidebar-brand
{
    background-color: #211746
}

a, .theme-white a:hover
{
    color: #d2b585;
}

.activeLi,
.sidebar-menu li a:hover {
    background-color: #d2b585 !important;
}

.btn-secondary:not(.btn-social):not(.btn-social-icon):hover
{
    background-color: #cdd3d8 !important;
    opacity: 0.8;
}

.sidebar-mini .sidebar-menu li a:hover i,
.sidebar-mini .sidebar-menu li a:hover::after {
    color: #d2b585 !important;
}

.light-sidebar.sidebar-mini .main-sidebar .sidebar-menu {
    margin-top: -5px;
}

.badge.badge-danger,
.bg-danger {
    color: #FFF !important;
    background-color: #ed4c78 !important;
}

.theme-white .page-item.active .page-link {
    background-color: #000;
    border-color: #000;
}
/* END change color */

.form-table {
    font-size: 13px;
}

.form-table td {
    padding: 5px !important;
}

.form-table td:first-child {
    padding-left: 0px !important;
    height: auto !important;
}

/* calendar time select */
.drp-calendar.single {
    max-width: unset !important;
    padding-right: 10px !important;
}

.calendar-time .select2.select2-container .select2-selection--single {
    height: auto;
    min-height: auto;
}

.calendar-time .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: auto;
    min-height: auto;
    top: 50%;
}

.calendar-time .select2.select2-container--default .select2-selection--single .select2-selection__rendered {
    padding-top: 5px;
    padding-left: 5px;
    padding-right: 15px;
    padding-bottom: 5px;
    min-height: auto;
}

.daterangepicker .cancelBtn {
    background-color: #cdd3d8;
}

.daterangepicker .cancelBtn.btn:not(.btn-social):not(.btn-social-icon):hover {
    background-color: #cdd3d8 !important;
    opacity: 0.8;
}

.clear-date {
    position: relative;
}

.clear-date i.fa {
    position: absolute;
    top: 36%;
    right: 5px;
    cursor: pointer;
    color: #999;
}

.clear-date i.fa:hover {
    color: #000
}
/* END calendar time select */

.info-table {
    margin-top: 5px;
    margin-bottom: 0px;
}

.info-table td {
    border: 1px solid #ddd;
    height: auto !important;
    padding: 7px 10px !important;
}

.btn {
    border-radius: 30px;
}

.btn.btn-sm {
    padding-left: 15px;
    padding-right: 15px;
    font-weight: normal;
    padding-top: 3px;
    padding-bottom: 3px;
}

.navbar .btn {
    border-radius: 5px;
}

.input-group-text {
    background: #EEE;
}

@media (min-width:992px)
{
    .main-sidebar
    {
        width: 220px;
    }

    .navbar
    {
        left: 220px;
    }

    .main-content
    {
        padding-left: 250px;
    }
}

@media (max-width: 1024px)
{
    .navbar
    {
        left: 0px;
    }
}

@media (max-width: 600px)
{
    .data-filter
    {
        flex-wrap: wrap;
    }

    .data-filter > .dataTables_length
    {
        order: 1;
    }

    .data-filter > .dataTables_length > label,
    .dataTables_filter,
    .dataTables_filter label
    {
        width: 100%;
    }

    .notification-icon .dropdown-menu
    {
        position: fixed;
        top: 70px;
        width: 100%;
    }

    .notification-icon .dropdown-menu.dropdown-menu-right
    {
        left: 0px;
        right: 0px;
        margin: 0px auto;
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.pac-container {
    z-index: 5000;
    position: absolute!important;
    background-color: #fff;
    border: 1px solid #ddd;
  }

.pac-item {
padding: 10px;
border-bottom: 1px solid #ddd;
}

.pac-item:hover {
background-color: #f5f5f5;
}

#address-search {
    width: 60%;
    top: 10px!important;
}

.map-picker {
    width: 40px;
    height: 40px;
}

.modal-body table.table,
.table-detail {
    margin-bottom: 5px;
}

.modal-body table.table thead th,
.table-detail thead th {
    height: unset !important;
    padding: 7px 10px !important;
    border-color: #DDD !important;
    border-bottom: 1px solid #DDD !important;
    font-size: 12px;
    font-weight: 600;
}

.modal-body table.table td,
table.table-detail td {
    border-color: #DDD !important;
    height: unset !important;
    padding-top: 7px !important;
    padding-bottom: 7px !important;
    font-size: 13px !important;
}

.btn-add-row {
    color: #d2b585 !important;
    font-size: 12px;
}

.table-detail .form-control,
.table-detail .input-group-text {
    font-size: 13px !important;
}

.table-icon-css.table-detail .btn-icon {
    padding-top: 0px;
}

.table-icon-css.table-detail .select2.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    min-height: unset;
}

.table-detail .select2.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    min-height: 40px;
}

/* dropzone */
.dropzone .dz-preview .dz-image {
    border: 1px solid #DDD;
    display: flex !important;
    align-items: center;
}

.dropzone .dz-preview .dz-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
    padding: 7px;
}

.theme-white .dropzone {
    border-color: #CCC;
}

.dropzone.dz-started .dz-message {
    display: block !important;
}

.dropzone .dz-message {
    color: #000;
    font-size: 20px;
}

.dropzone .dz-preview .dz-error-message {
    top: 10px !important;
}

.dropzone .dz-preview .dz-error-message:after {
    display: none;
}
/* END dropzone */

/* accordion */
.accordion {
    margin-bottom: 20px;
}

.accordion-title {
    font-size: 14px;
    background: #EEE;
    border: 1px solid #CCC;
    cursor: pointer;
    font-weight: 500;
    padding: 10px;
    display: flex;
    align-items: center;
    margin-bottom: 0px;
}

.accordion-title > div {
    flex-grow: 1;
}

.accordion-title > div > span {
    border-radius: 5px;
    border:1px solid #CCC;
    padding: 0px 7px;
    font-size: 14px;
    margin-right: 3px;
    font-weight: normal;
}

.accordion-title > i {
    font-size: 15px;
}

.accordion-content {
    border: 1px solid #CCC;
    border-top: 0px;
    padding: 7px 10px;
}
/* END accordion */

.row-gap-7 {
    row-gap:7px;
}

.modal {
    overflow: auto !important;
}

.select2-container--open {
    z-index: 9999999
}
