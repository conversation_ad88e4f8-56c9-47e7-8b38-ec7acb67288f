
<?php $__env->startSection('content'); ?>
    <span class="d-none" id="current-menu" data-menu="menu-products"></span>
    <div class="card mb-4">
        <div class="card-header">
            <h4 class="flex-grow-1">
                <?php echo e(__('Products')); ?>

                <div class="text-muted"><small><?php echo e(__('Search by name or sku')); ?></small></div>
            </h4>

            <div>
                <?php if(checkPermission('create_product')): ?>
                    <a href="<?php echo e(route('productsForm')); ?>" class="ml-md-auto my-1 btn btn-primary text-white"><?php echo e(__('Add Product')); ?></a>
                <?php endif; ?>
                <?php if(checkPermission('export_product')): ?>
                    <a href="" data-url="<?php echo e(route('productsExport')); ?>" id="<?php echo e($moduleID); ?>Export" class="my-1 ml-1 btn btn-secondary text-white"><?php echo e(__('Export')); ?></a>
                <?php endif; ?>
            </div>
        </div>
        <div class="card-body table-id" id="<?php echo e($moduleID); ?>List" data-module="<?php echo e($moduleID); ?>">
            <div class="form-row text-left justify-content-end mb-2 row-gap-7">
                <?php if(checkFeatureControl('filter', 'brand')): ?>
                <div class="col-md-3">
                    <select name="fbrand" class="form-control select2 filter-form" data-width="100%">
                        <option value=""><?php echo e(__('All Brand')); ?></option>
                        <?php $__currentLoopData = userBrandList(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($key); ?>"><?php echo e($item); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <?php endif; ?>
                <?php if(checkFeatureControl('filter', 'country')): ?>
                <div class="col-md-3">
                    <select name="fcountry" class="form-control select2 filter-form" data-width="100%">
                        <option value=""><?php echo e(__('All Countries')); ?></option>
                        <?php $__currentLoopData = config('staticdata.country'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($key); ?>"><?php echo e($item); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <?php endif; ?>
                <div class="col-md-3">
                    <select name="fproductcategory" class="form-control select2 filter-form" data-width="100%" onchange="getProductSubcategory(this.value)">
                        <option value=""><?php echo e(__('All Product Category')); ?></option>
                        <?php $__currentLoopData = $productCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($item->id); ?>"><?php echo e($item->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="fproductsubcategory" class="form-control select2 filter-form" id="fproductsubcategory" data-width="100%">
                        <option value=""><?php echo e(__('All Product Subcategory')); ?></option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <select name="fstatus" class="form-control select2 filter-form" data-width="100%">
                        <option value="all"><?php echo e(__('All Statuses')); ?></option>
                        <option value="1"><?php echo e(__('Enabled')); ?></option>
                        <option value="0"><?php echo e(__('Disabled')); ?></option>
                    </select>
                </div>
                
                
            </div>
            <?php if($bulk_action): ?>
                <?php echo $__env->make('include.selected-box', [ 
                    'bulk_action' => $bulk_action,
                    'datatable_list' => $moduleID.'List'
                ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
            <div class="table-responsive">
                <table class="table table-striped word-wrap" data-url="<?php echo e(route('productsList')); ?>" data-delete="<?php echo e(route('productsDelete')); ?>" data-status="<?php echo e(route('productsStatusUpdate')); ?>">
                    <thead>
                        <tr>
                            <?php if($bulk_action): ?>
                            <th width="40px">
                                <label class="custom-checkbox">
                                    <input type="checkbox" value="1" class="form-check-input checkall" data-datatable="<?php echo e($moduleID); ?>List">
                                    <span></span>
                                </label>
                            </th>
                            <?php endif; ?>
                            <th style="min-width:180px" data-column="name" class="sortable"><?php echo e(__('Name')); ?></th>
                            <th style="min-width:100px" data-column="sku" class="sortable"><?php echo e(__('SKU')); ?></th>
                            <th style="min-width:100px" data-column="product_category_id" class="sortable"><?php echo e(__('Product Category')); ?></th>
                            <th style="min-width:100px" data-column="product_subcategory_id" class="sortable"><?php echo e(__('Product Subcategory')); ?></th>
                            <th style="min-width:100px" data-column="is_out_of_stock" class="sortable"><?php echo e(__('Out Of Stock?')); ?></th>
                            <!-- <th style="min-width:150px"><?php echo e(__('Brand')); ?></th>
                            <th style="min-width:150px"><?php echo e(__('Country')); ?></th>
                            <th style="min-width:150px"><?php echo e(__('Price Group')); ?></th> -->
                            <th style="min-width:100px" class="text-center"><?php echo e(__('Enabled')); ?></th>
                            <th style="min-width:120px" data-column="created_at" class="sortable"><?php echo e(__('Created On')); ?></th>
                            <?php if(checkPermission('edit_product') || checkPermission('delete_product')): ?>
                            <th width="100px" class="text-center"><?php echo e(__('Action')); ?></th>
                            <?php endif; ?>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
            <input type="hidden" name="sort_col" id="sort_col" value="<?php echo e(request()->sort_col); ?>">
            <input type="hidden" name="sort_by" id="sort_by" value="<?php echo e(request()->sort_by); ?>">
        </div>
    </div>
    <script>
        function getProductSubcategory (categoryId) {
            $.ajax({
                url: '<?php echo e(route('productsAjaxProductSubcategory')); ?>?category_id='+categoryId+'&filter=1',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    var optionList = response.data;
                    var optionSelect = $('#fproductsubcategory');
                    optionSelect.empty();
                    optionSelect.append(optionList);
                },
                error: (error) => {
                    console.log(JSON.stringify(error));
                },
            });
        }
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('include.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\MBB_Backend\resources\views/products.blade.php ENDPATH**/ ?>