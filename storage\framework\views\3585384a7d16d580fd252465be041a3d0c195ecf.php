<!DOCTYPE html>
<html lang="en">
<head>
    <?php
        $home_url = env('APP_URL');
        $logo = global_settings('admin_logo');
        $portal = 'admin';

        if(request()->route()->getPrefix() == '/retailer'):
            $portal = 'retailer';
            $home_url = env('APP_URL').'/retailer';
        endif;

        session()->put('portal', $portal);
    ?>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, shrink-to-fit=no" name="viewport">
    <title>
        <?php echo e(global_settings('site_name')); ?>

    </title>

    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo e(asset('asset/css/app.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('/asset/css/style.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('/asset/css/components.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('/asset/css/custom.css')); ?>?v=<?php echo e(time()); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('/asset/css/loginPage.css')); ?>">
    <link rel='shortcut icon' type='image/x-icon' href='<?php echo e(asset('/asset/img/favicon.ico')); ?>' />
    <style type="text/css">
        *
        {
            font-family: "Poppins", sans-serif;
            font-size: 16px;
        }

        .white-box
        {
            background: #ffffff;
            box-shadow: 0px 4px 43px rgba(0, 0, 0, 0.08);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            min-width: 450px;
        }

        .btn
        {
            font-size: 14px;
            font-weight: normal;
            margin-top: 25px;
            cursor:pointer;
        }

        p
        {
            line-height: 1.5;
        }
    </style>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
    <script>var domainUrl = "<?php echo e(env('APP_URL')); ?>/";</script>
    <script src="<?php echo e(asset('asset/script/env.js')); ?>"></script>
    <?php echo $__env->yieldContent('header'); ?>
</head>

<body class="dark">
    <div class="main-login-row">
        <div class="mx-auto">
            <div class="center">
                <a href="<?php echo e($home_url); ?>" style="text-decoration:none" class="mb-1">
                    <?php if($logo): ?>
                        <img src='<?php echo e(\App\Models\GlobalFunction::createMediaUrl($logo)); ?>' style='max-width:250px'>
                    <?php else: ?>
                        <span class="logo-name font-34"><?php echo e(global_settings('site_name')); ?></span>
                    <?php endif; ?>
                </a>

                <?php if($portal == 'doctor'): ?>
                    <div class="font-20 mt-2 fw-500"><?php echo e(__('ECP Dashboard')); ?></div>
                <?php elseif($portal == 'retailer'): ?>
                    <div class="font-20 mt-2 fw-500" style="color:#FFF"><?php echo e(__('Retailer Dashboard')); ?></div>
                <?php endif; ?>

                <?php if(Session::has('error')): ?>
                    <div class="alert alert-danger text-center font-14 py-2 mt-4 mb-0 w-100">
                        <?php echo e(Session::get('error')); ?>

                    </div>
                <?php endif; ?>
                <?php if(Session::has('message')): ?>
                    <div class="alert alert-success text-center font-14 mt-4 mb-0 w-100">
                        <?php echo e(Session::get('message')); ?>

                    </div>
                <?php endif; ?>

                <div class="form-login-main-box mt-4 white-box">
                    <?php echo $__env->yieldContent('content'); ?>
                </div>
            </div>
        </div>
    </div>

    <script src="<?php echo e(asset('/asset/js/app.min.js').'?v='.time()); ?>"></script>
    <script src="<?php echo e(asset('/asset/js/scripts.js').'?v='.time()); ?>"></script>
    <script src="<?php echo e(asset('/asset/js/custom.js').'?v='.time()); ?>"></script>
</body>
</html><?php /**PATH C:\Users\<USER>\Documents\GitHub\MBB_Backend\resources\views/non-auth/layout.blade.php ENDPATH**/ ?>