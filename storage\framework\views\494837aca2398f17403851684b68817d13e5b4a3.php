
<?php $__env->startSection('content'); ?>
    <form method="POST" action="login" class="text-left">
        <?php echo csrf_field(); ?>
        <div>
            <div class="d-flex flex-column mb-3 w-100">
                <label class="gil-med font-16 text-salon-black"><?php echo e(__('Username')); ?> (<?php echo e(__('Email Address')); ?> Test)</label>
                <input name="user_name" type="email" class="login-fild gil-med font-16 px-3" required>
            </div>
            <div class="d-flex flex-column mb-3 w-100">
                <label class="gil-med font-16 text-salon-black"><?php echo e(__('Password')); ?></label>
                <div class="password-box">
                    <input name="user_password" type="password" class="login-fild gil-med font-16 px-3 w-100" required>
                    <i class="fa fa-eye btn-toggle-password"></i>
                </div>
            </div>
        </div>
        
        <div>
            <a href="<?php echo e(request()->route()->getPrefix() == '/retailer' ? route('retailer.forgotPassword') : route('forgotPassword')); ?>" class="gil-med font-15"><?php echo e(__('Forgot your password?')); ?></a>
        </div>

        <button type="submit" class="btn btn-primary mt-4 p-2 w-100">
            <?php echo e(__('Login')); ?>

        </button>
    </form>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('non-auth.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\MBB_Backend\resources\views/login.blade.php ENDPATH**/ ?>