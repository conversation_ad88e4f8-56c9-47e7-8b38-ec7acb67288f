<div class="card mb-4">
    <div class="card-header">
        <h4 class="flex-grow-1">
            <?php echo e($title); ?>

        </h4>
    </div>

    <div class="card-body">
        <form id="detail-form" autocomplete="off" class="mb-0" action="<?php echo e(route('productsAddUpdate')); ?>" method="post" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            <div class="row">
                <div class="col-md-12 form-group">
                    <label>
                        <?php echo e(__('Name')); ?>

                        <span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control" name="name" value="<?php echo e($result->name ?? ''); ?>" required>
                </div>

                <div class="col-md-6 form-group">
                    <label>
                        <?php echo e(__('Product Category')); ?>

                        <span class="text-danger">*</span>
                    </label>
                    <select name="product_category_id" class="form-control select2" required onchange="getProductSubcategory(this.value)">
                        <option value="">Select Product Category</option>
                        <?php $__currentLoopData = $product_categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($item->id); ?>" <?php echo e($result && $result->product_category_id == $item->id ? 'selected' : ''); ?>><?php echo e($item->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="col-md-6 form-group">
                    <label>
                        <?php echo e(__('Product Subcategory')); ?>

                        <span class="text-danger">*</span>
                    </label>
                    <select name="product_subcategory_id" class="form-control select2" id="subcategory_id" required>
                    </select>
                </div>

                <div class="col-md-6 form-group">
                    <label>
                        <?php echo e(__('SKU')); ?>

                        <span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control" name="sku" value="<?php echo e($result->sku ?? ''); ?>" required>
                </div>
                

                <div class="col-md-6 form-group">
                    <label>
                        <?php echo e(__('Price (RM)')); ?>

                    </label>
                    <input type="text" class="form-control" name="price" value="<?php echo e($result->price ?? ''); ?>">
                </div>
                <div class="col-md-6 form-group">
                    <label>
                        <?php echo e(__('URL Link')); ?>

                    </label>
                    <input type="text" class="form-control" name="url" value="<?php echo e($result->url ?? ''); ?>">
                </div>

                <div class="form-group col-md-6">
                    <label>
                        <?php echo e(__('Country')); ?>

                        <span class="text-danger">*</span>
                    </label>
                    <select name="country[]" class="form-control select2" data-width="100%" data-placeholder="<?php echo e(__('Select')); ?>" multiple required>
                        <?php $__currentLoopData = config('staticdata.country'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($key); ?>" <?php echo e($result->country && in_array($key, $result->country) ? 'selected' : ''); ?>><?php echo e($item); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <div class="col-md-6 form-group" id="weight-field" hidden>
                    <label>
                        <?php echo e(__('Weight (KG)')); ?>

                    </label>
                    <input type="text" class="form-control" name="weight" value="<?php echo e($result->weight ?? ''); ?>">
                </div>

                <div class="col-md-12 form-group">
                    <label>
                        <?php echo e(__('Manual Handle Area')); ?>

                    </label>
                        <div class="table-responsive">
                        <table id="manual-handle-table" class="table table-detail table-icon-css">
                            <thead>
                                <tr>
                                    <th width="300px"><?php echo e(__('Postcode')); ?></th>
                                    <th width="80px" class="text-center"><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                    $postcodes = is_array($result->manual_postcode) ? $result->manual_postcode : json_decode($result->manual_postcode ?? '[]', true);
                                ?>

                                <?php $__currentLoopData = $postcodes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $postcode): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <input type="text" name="manual_postcode[]" class="form-control" value="<?php echo e($postcode); ?>" placeholder="<?php echo e(__('Enter Postcode')); ?>">
                                        </td>
                                        <td class="text-center">
                                            <button type="button" class="btn-icon btn-danger btn-remove-row"><i class="fa fa-trash-alt"></i></button>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                <tr hidden>
                                    <td>
                                        <input type="text" name="manual_postcode[new_row]" class="form-control" placeholder="<?php echo e(__('Enter Postcode')); ?>">
                                    </td>
                                    <td class="text-center">
                                        <button type="button" class="btn-icon btn-danger btn-remove-row"><i class="fa fa-trash-alt"></i></button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                        <a href="" class="btn-add-row" data-table="manual-handle-table"><i class="fa fa-plus"></i> <?php echo e(__('Add More Postcode')); ?></a>
                    </div>
                </div>

            </div>

            <div class="row">
                <div class="col-md-3 form-group">
                    <label><?php echo e(__('Enabled')); ?></label>
                    <div class="d-flex align-items-center">
                        <label class="switch mb-0">
                            <input type="checkbox" name="is_active" id="is_active" value="1" class="enable" <?php echo e($result->is_active == 1 ? 'checked' : ''); ?>>
                            <span class="slider round"></span>
                        </label>
                        <label for="is_active" class="mb-0 ml-1 cursor-pointer"><?php echo e(__('Yes')); ?></label>
                    </div>
                </div>

                <div class="col-md-3 form-group">
                    <label><?php echo e(__('Out Of Stock?')); ?></label>
                    <div class="d-flex align-items-center">
                        <label class="switch mb-0">
                            <input type="checkbox" name="is_out_of_stock" id="is_out_of_stock" value="1" class="enable" <?php echo e($result->is_out_of_stock == 1 ? 'checked' : ''); ?>>
                            <span class="slider round"></span>
                        </label>
                        <label for="is_out_of_stock" class="mb-0 ml-1 cursor-pointer"><?php echo e(__('Yes')); ?></label>
                    </div>
                </div>
            </div>

            <div class="row">
                

                <div class="col-md-12 form-group mb-3" id="variant-attribute-box" <?php echo e($result->is_variant == 1 ? '' : 'hidden'); ?>>
                    <label>
                        <?php echo e(__('Variant Attributes')); ?>

                    </label>
                    <div class="table-responsive">
                        <table id="attribute-table" class="table table-bordered table-detail table-icon-css">
                            <thead>
                                <tr>
                                    <th width="300px"><?php echo e(__('Option')); ?></th>
                                    <th style="min-width:200px"><?php echo e(__('Values')); ?></th>
                                    <th width="80px" class="text-center"><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr hidden>
                                    <td>
                                        <select name="option_id[new_row]" class="form-control select2 option_id" data-width="100%">
                                            <option value=""><?php echo e(__('Select Option')); ?></option>
                                            <?php $__currentLoopData = $options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($item->id); ?>"><?php echo e($item->name); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </td>
                                    <td>
                                        <select name="attribute_values[new_row][]" class="form-control select2" data-width="100%" data-placeholder="<?php echo e(__('Select Values')); ?>" multiple>
                                        </select>
                                    </td>
                                    <td class="text-center">
                                        <button type="button" class="btn-icon btn-danger btn-remove-row" data-toggle="tooltip" data-title="Delete"><i class="fa fa-trash-alt"></i></button>
                                    </td>
                                </tr>
                                <?php if(!$attributes): ?>
                                    <tr>
                                        <td>
                                            <select name="option_id[0]" class="form-control select2 option_id" data-width="100%">
                                                <option value=""><?php echo e(__('Select Option')); ?></option>
                                                <?php $__currentLoopData = $options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($item->id); ?>"><?php echo e($item->name); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </td>
                                        <td>
                                            <select name="attribute_values[0][]" class="form-control select2" data-width="100%" data-placeholder="<?php echo e(__('Select Values')); ?>" multiple>
                                            </select>
                                        </td>
                                        <td></td>
                                    </tr>
                                <?php else: ?>
                                    <?php $__currentLoopData = $attributes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <select name="option_id[<?php echo e($key); ?>]" class="form-control select2 option_id" data-width="100%">
                                                    <option value=""><?php echo e(__('Select Option')); ?></option>
                                                    <?php $__currentLoopData = $options; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($option->id); ?>" <?php echo e($item->option_id == $option->id ? 'selected' : ''); ?>><?php echo e($option->name); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </td>
                                            <td>
                                                <select name="attribute_values[<?php echo e($key); ?>][]" class="form-control select2" data-width="100%" data-placeholder="<?php echo e(__('Select Values')); ?>" multiple>
                                                    <?php $__currentLoopData = $item->values; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($value); ?>" selected><?php echo e($value); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </td>
                                            <td>
                                                <?php if($key != 0): ?>
                                                    <button type="button" class="btn-icon btn-danger btn-remove-row" data-toggle="tooltip" data-title="Delete"><i class="fa fa-trash-alt"></i></button>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    <div>
                        <a href="" class="btn-add-row" data-table="attribute-table"><i class="fa fa-plus"></i> <?php echo e(__('Add More Attribute')); ?></a>
                    </div>
                </div>

                <?php if(checkFeatureControl('selector', 'brand')): ?>
                <div class="form-group col-md-6">
                    <label>
                        <?php echo e(__('Brand')); ?>

                        <span class="text-danger">*</span>
                    </label>
                    <select name="brand" class="form-control select2" data-width="100%" required>
                        <option value=""><?php echo e(__('Select Brand')); ?></option>
                        <?php $__currentLoopData = userBrandList(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($key); ?>" <?php echo e($result && $result->brand == $key ? 'selected' : ''); ?>><?php echo e($item); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <?php endif; ?>

                

                <div class="col-md-12 form-group">
                    <label>
                        <?php echo e(__('Short Description')); ?>

                    </label>
                    <textarea class="form-control" name="short_desc"><?php echo e($result->short_desc ?? ''); ?></textarea>
                </div>
                <div class="col-md-12 form-group mb-3">
                    <label>
                        <?php echo e(__('Description')); ?>

                    </label>
                    <textarea id="summernote" class="summernote-simple" name="desc"><?php echo e($result->desc ?? ''); ?></textarea>
                </div>
                <div class="col-md-12 form-group">
                    <label><?php echo e(__('Images')); ?></label>
                    <div class="dropzone" id="product-images"></div>
                </div>
            </div>

            <input type="hidden" name="id" value="<?php echo e($id); ?>">
            <input type="hidden" name="tab" value="<?php echo e($tab); ?>">
            <input type="hidden" name="product_category" value="<?php echo e($product_category); ?>">
            <input type="hidden" name="product_subcategory" value="<?php echo e($product_subcategory); ?>">
            <button class="btn btn-primary btn-lg mb-2" type="submit"><?php echo e(__('Submit')); ?></button>
        </form>
    </div>
</div>

<?php echo $__env->make('include.dropzone_js', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<script>
    $(document).ready(function() {
        <?php if($result->is_variant == 1): ?>
            $.each($('.option_id'), function(index, elem) {
                if($(elem).val()) {
                    changeOption($(elem));
                }
            });
        <?php endif; ?>

        $(document).on('change', '.option_id', function() {
            changeOption($(this));
        });

        $(document).on('click', '#is_variant', function() {
            if($(this).prop('checked')) {
                $('#variant-attribute-box').attr('hidden', false);
            } else {
                $('#variant-attribute-box').attr('hidden', true);
            }
        });

        $(document).on('submit', '#detail-form', function(e) {
            var is_variant = $('#is_variant').prop('checked');
            var manual_postcode = $('input[name="manual_postcode[]"]');
            var postcodes = [];
            var duplicatePostcode = false;
            if(is_variant) {
                var selected = [];
                $.each($('tr:not(:first-child) select[name^="option_id"]'), function(index, value) {
                    var option_id = $(this).val();
                    if(!option_id) {
                        swal('<?php echo e(__('Error')); ?>', '<?php echo e(__('Please select option')); ?>', 'error');
                        e.preventDefault();
                    }
                    selected.push(option_id);

                    var attribute_values = $(this).closest('tr').find('select[name^="attribute_values"]').val();
                    if(attribute_values == '') {
                        swal('<?php echo e(__('Error')); ?>', '<?php echo e(__('Please select value')); ?>', 'error');
                        e.preventDefault();
                    }
                });

                if(selected.length != [...new Set(selected)].length) {
                    swal('<?php echo e(__('Error')); ?>', '<?php echo e(__('Duplicate option selected')); ?>', 'error');
                    e.preventDefault();
                }
            }

            manual_postcode.each(function() {
            var postcode = $(this).val().trim();
            if (postcode) {
                if (postcodes.includes(postcode)) {
                    duplicatePostcode = true;
                    return false;
                }
                postcodes.push(postcode);
            }
        });

        if (duplicatePostcode) {
            swal('<?php echo e(__("Error")); ?>', '<?php echo e(__("Duplicate postcode found")); ?>', 'error');
            e.preventDefault();
        }

        });
    });

    function changeOption(elem){
        var option_id = elem.val(); 
        var row = elem.closest('tr');
        var selected = elem.closest('tr').find('select[name^="attribute_values"]').val();

        $.ajax({
            url: '<?php echo e(route('attributesAjaxValue')); ?>?option_id='+option_id,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                var optionList = response.data;
                $.each(selected, function(index, value) {
                    optionList = optionList.replace('value="'+value+'"', 'value="'+value+'" selected');
                });

                var optionSelect = row.find('select[name^="attribute_values"]');
                optionSelect.empty();
                optionSelect.append(optionList);

                optionSelect.select2({
                    width: '100%',
                    placeholder: '<?php echo e(__('Select Values')); ?>',
                    multiple: true,
                });
            },
            error: (error) => {
                console.log(JSON.stringify(error));
            },
        });
    }

    var product_subcategory = $('input[name="product_subcategory"]').val();
    var product_category = $('input[name="product_category"]').val();

    if(product_category != null) {
        getProductSubcategory(product_category, product_subcategory);
    }

    function getProductSubcategory (categoryId, product_subcategory) {
        $.ajax({
            url: '<?php echo e(route('productsAjaxProductSubcategory')); ?>?category_id='+categoryId + '&product_subcategory=' + product_subcategory,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                var optionList = response.data.html;
                var optionSelect = $('#subcategory_id');
                optionSelect.empty();
                optionSelect.append(optionList);

                if (response.data.product_weight == 1) {
                    $('#weight-field').removeAttr('hidden');
                } else {
                    $('#weight-field').attr('hidden', true);
                }
            },
            error: (error) => {
                console.log(JSON.stringify(error));
            },
        });
    }

</script><?php /**PATH C:\Users\<USER>\Documents\GitHub\MBB_Backend\resources\views/product-form-details.blade.php ENDPATH**/ ?>