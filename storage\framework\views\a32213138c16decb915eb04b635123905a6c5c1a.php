<script>
    $.each($('.dropzone'), function (index, element) {
        var uploadedFiles = {}
        var default_max_filesize = 5; // mb
        var default_accepted_files = 'image/*';
        if(typeof $(element).data('max-filesize') !== 'undefined') {
            default_max_filesize = $(element).attr('data-max-filesize');
        }
        if(typeof $(element).data('accepted-files') !== 'undefined') {
            default_accepted_files = $(element).attr('data-accepted-files');
        }

        var $this = $(element)
        var url = '<?php echo e(route('mediaStore')); ?>'
        var maxFilesize = default_max_filesize
        var acceptedFiles = default_accepted_files
        var addRemoveLinks = true
        var headers = {
            'X-CSRF-TOKEN': "<?php echo e(csrf_token()); ?>"
        }
        var success = function (file, response) {
            $this.append('<input type="hidden" name="files[]" value="' + response.name + '">')
            uploadedFiles[file.name] = response.name
        }
        var removedfile = function (file) {
            file.previewElement.remove()
            var name = ''
            if (typeof file.file_name !== 'undefined') {
                name = file.file_name
            } else {
                name = uploadedFiles[file.name]
            }
            $this.find('input[name="files[]"][value="' + name + '"]').remove()
        }
        var init = function () {
            <?php if(isset($result) && $result->files): ?>
                var files = <?php echo json_encode($result->files); ?>

                for (var i in files) {
                    var file = files[i]
                    this.options.addedfile.call(this, file);
                    this.options.thumbnail.call(this, file, file.preview_url);
                    file.previewElement.classList.add('dz-complete')
                    $this.append('<input type="hidden" name="files[]" value="' + file.file_name + '">')
                }
            <?php endif; ?>
        }
        var options = {
            url: '<?php echo e(route('mediaStore')); ?>',
            maxFilesize: maxFilesize,
            acceptedFiles: acceptedFiles,
            addRemoveLinks: addRemoveLinks,
            headers: headers,
            success: success,
            removedfile: removedfile,
            init: init
        }
        $(`#${$this.attr('id')}`).dropzone(options)
    })
</script><?php /**PATH C:\Users\<USER>\Documents\GitHub\MBB_Backend\resources\views/include/dropzone_js.blade.php ENDPATH**/ ?>