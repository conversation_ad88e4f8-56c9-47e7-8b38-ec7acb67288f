<form action="<?php echo e(route('productSubcategoryAddUpdate')); ?>" method="post" enctype="multipart/form-data" id="<?php echo e($moduleID); ?>Form" autocomplete="off">
    <?php echo csrf_field(); ?>
    <div class="row">
        <div class="form-group col-md-6">
            <label>
                <?php echo e(__('Name')); ?>

                <span class="text-danger">*</span>
            </label>
            <input type="text" name="name" id="editName" class="form-control" required>
        </div>

        <div class="form-group col-md-6">
            <label>
                <?php echo e(__('Product Category')); ?>

                <span class="text-danger">*</span>
            </label>
            <select name="product_category_id" id="editProductCategoryId" class="form-control select2" data-width="100%" required>
                <option value="">Select Product Category</option>
                <?php $__currentLoopData = $productCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <option value="<?php echo e($productCategory->id); ?>"><?php echo e($productCategory->name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
        </div>
            
    
        <div class="form-group col-md-6">
            <label>
                <?php echo e(__('Enable')); ?>

            </label>
            <div class="d-flex align-items-center">
                <label class="switch mb-0">
                    <input type="checkbox" name="status" id="editStatus" value="1" class="enable" checked>
                    <span class="slider round"></span>
                </label>
                <label for="editStatus" class="mb-0 ml-1 cursor-pointer"><?php echo e(__('Yes')); ?></label>
            </div>
        </div>
        <div class="form-group col-md-6">
            <label>
                <?php echo e(__('Product Weight')); ?>

            </label>
            <div class="d-flex align-items-center">
                <label class="switch mb-0">
                    <input type="checkbox" name="product_weight" id="editProductWeight" value="1" class="enable" checked>
                    <span class="slider round"></span>
                </label>
                <label for="editProductWeight" class="mb-0 ml-1 cursor-pointer"><?php echo e(__('Yes')); ?></label>
            </div>
            <label for="editProductWeight" style="font-weight: 400; color: red"><?php echo e(__('*Disable if product does not have a set weight')); ?></label>
        </div>
    </div>

    <input type="hidden" name="id" id="editId">
    <input class="btn btn-primary btn-lg" type="submit" value=" <?php echo e(__('Submit')); ?>">
</form>
    <?php /**PATH C:\Users\<USER>\Documents\GitHub\MBB_Backend\resources\views/modals/product-subcategory-add.blade.php ENDPATH**/ ?>