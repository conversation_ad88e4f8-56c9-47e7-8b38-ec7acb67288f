[2025-10-06 21:11:41] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (SQL: select * from `settings` where `name` = smtp_host limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (SQL: select * from `settings` where `name` = smtp_host limit 1) at C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\helpers.php(13): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\Providers\\AppServiceProvider.php(58): global_settings('smtp_host')
#11 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\Providers\\AppServiceProvider.php(34): App\\Providers\\AppServiceProvider->initConfigFromDB()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(926): Illuminate\\Container\\Container->call(Array)
#18 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(907): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 32)
#20 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(906): array_walk(Array, Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=127....', 'mybolehboleh', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'mybolehboleh', 'GYO6_rrwfi4@mqo...', Array)
#2 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\helpers.php(13): Illuminate\\Database\\Eloquent\\Builder->first()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\Providers\\AppServiceProvider.php(58): global_settings('smtp_host')
#21 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\Providers\\AppServiceProvider.php(34): App\\Providers\\AppServiceProvider->initConfigFromDB()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(926): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(907): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#29 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 32)
#30 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(906): array_walk(Array, Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#32 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#34 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#35 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}
"} 
[2025-10-06 21:13:59] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'mybolehboleh'@'localhost' (using password: YES) (SQL: select * from `settings` where `name` = smtp_host limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'mybolehboleh'@'localhost' (using password: YES) (SQL: select * from `settings` where `name` = smtp_host limit 1) at C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\helpers.php(13): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\Providers\\AppServiceProvider.php(58): global_settings('smtp_host')
#11 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\Providers\\AppServiceProvider.php(34): App\\Providers\\AppServiceProvider->initConfigFromDB()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(926): Illuminate\\Container\\Container->call(Array)
#18 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(907): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 32)
#20 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(906): array_walk(Array, Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'mybolehboleh'@'localhost' (using password: YES) at C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=127....', 'mybolehboleh', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'mybolehboleh', 'GYO6_rrwfi4@mqo...', Array)
#2 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\helpers.php(13): Illuminate\\Database\\Eloquent\\Builder->first()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\Providers\\AppServiceProvider.php(58): global_settings('smtp_host')
#21 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\Providers\\AppServiceProvider.php(34): App\\Providers\\AppServiceProvider->initConfigFromDB()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(926): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(907): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#29 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 32)
#30 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(906): array_walk(Array, Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#32 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#34 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#35 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}
"} 
[2025-10-06 21:23:07] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'mybolehboleh'@'localhost' (using password: YES) (SQL: select * from `settings` where `name` = smtp_host limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'mybolehboleh'@'localhost' (using password: YES) (SQL: select * from `settings` where `name` = smtp_host limit 1) at C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\helpers.php(13): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\Providers\\AppServiceProvider.php(58): global_settings('smtp_host')
#11 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\Providers\\AppServiceProvider.php(34): App\\Providers\\AppServiceProvider->initConfigFromDB()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#13 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(926): Illuminate\\Container\\Container->call(Array)
#18 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(907): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 32)
#20 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(906): array_walk(Array, Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#25 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'mybolehboleh'@'localhost' (using password: YES) at C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=127....', 'mybolehboleh', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'mybolehboleh', 'GYO6_rrwfi4@mqo...', Array)
#2 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#13 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#16 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#17 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#18 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#19 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\helpers.php(13): Illuminate\\Database\\Eloquent\\Builder->first()
#20 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\Providers\\AppServiceProvider.php(58): global_settings('smtp_host')
#21 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\Providers\\AppServiceProvider.php(34): App\\Providers\\AppServiceProvider->initConfigFromDB()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#23 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(926): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(907): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#29 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 32)
#30 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(906): array_walk(Array, Object(Closure))
#31 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#32 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#34 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#35 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}
"} 
[2025-10-06 21:24:24] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (SQL: select * from `settings` where `name` = smtp_host limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (SQL: select * from `settings` where `name` = smtp_host limit 1) at C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:712)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(784): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(763): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(674): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#11 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\helpers.php(13): Illuminate\\Database\\Eloquent\\Builder->first()
#12 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\Providers\\AppServiceProvider.php(58): global_settings('smtp_host')
#13 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\Providers\\AppServiceProvider.php(34): App\\Providers\\AppServiceProvider->initConfigFromDB()
#14 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#15 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(926): Illuminate\\Container\\Container->call(Array)
#20 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(907): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#21 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 32)
#22 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(906): array_walk(Array, Object(Closure))
#23 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#25 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#26 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#27 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:70)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(70): PDO->__construct('mysql:host=127....', 'mybolehboleh', Object(SensitiveParameterValue), Array)
#1 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(45): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'mybolehboleh', 'GYO6_rrwfi4@mqo...', Array)
#2 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1064): call_user_func(Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1100): Illuminate\\Database\\Connection->getPdo()
#7 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(442): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(368): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(705): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#10 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(784): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(763): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(674): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(359): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2413): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#15 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2402): Illuminate\\Database\\Query\\Builder->runSelect()
#16 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2936): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#17 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2401): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(625): Illuminate\\Database\\Query\\Builder->get(Array)
#19 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(609): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#20 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(294): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#21 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\helpers.php(13): Illuminate\\Database\\Eloquent\\Builder->first()
#22 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\Providers\\AppServiceProvider.php(58): global_settings('smtp_host')
#23 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\app\\Providers\\AppServiceProvider.php(34): App\\Providers\\AppServiceProvider->initConfigFromDB()
#24 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Providers\\AppServiceProvider->boot()
#25 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(40): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(653): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(926): Illuminate\\Container\\Container->call(Array)
#30 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(907): Illuminate\\Foundation\\Application->bootProvider(Object(App\\Providers\\AppServiceProvider))
#31 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(App\\Providers\\AppServiceProvider), 32)
#32 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(906): array_walk(Array, Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#34 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#35 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#36 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#37 C:\\Users\\<USER>\\Documents\\GitHub\\MBB_Backend\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 {main}
"} 
