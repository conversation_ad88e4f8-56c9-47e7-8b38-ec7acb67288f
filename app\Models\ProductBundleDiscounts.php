<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductBundleDiscounts extends Model
{
    use HasFactory;

    protected $appends = ['membership_tiers'];

    public function product_bundle()
    {
        return $this->belongsTo(ProductBundle::class, 'product_bundle_id');
    }

    public function product_variant()
    {
        return $this->belongsTo(ProductVariations::class, 'product_variant_id');
    }

    public function getPackageNameAttribute()
    {
        return $this->quantity . ' @ ' . formatNumber($this->price, 2, 1);
    }
}
