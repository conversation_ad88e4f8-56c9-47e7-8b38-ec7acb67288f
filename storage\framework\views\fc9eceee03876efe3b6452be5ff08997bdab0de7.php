
<?php $__env->startSection('content'); ?>
<span class="d-none" id="current-menu" data-menu="menu-product-categories"></span>
    <div class="card mb-4">
        <div class="card-header">
            <h4 class="flex-grow-1">
                <?php echo e(__('Product Category')); ?>

                <div class="text-muted"><small><?php echo e(__('Search by name')); ?></small></div>
            </h4>

            <div>
                <?php if(checkPermission('create_product_category')): ?>
                    <a id="<?php echo e($moduleID); ?>Add" href="" class="ml-md-auto my-1 btn btn-primary text-white"><?php echo e(__('Add Product Category')); ?></a>
                <?php endif; ?>
                <?php if(checkPermission('export_product_category')): ?>
                    <a href="" data-url="<?php echo e(route('productCategoryExport')); ?>" id="<?php echo e($moduleID); ?>Export" class="my-1 ml-1 btn btn-secondary text-white"><?php echo e(__('Export')); ?></a>
                <?php endif; ?>
            </div>
        </div>
        <div class="card-body table-id" id="<?php echo e($moduleID); ?>List" data-module="<?php echo e($moduleID); ?>">
            <div class="form-row text-left justify-content-end mb-2">
                <div class="col-md-3">
                    <select name="fstatus" class="form-control select2 filter-form" data-width="100%">
                        <option value="all"><?php echo e(__('All Statuses')); ?></option>
                        <option value="1"><?php echo e(__('Enabled')); ?></option>
                        <option value="0"><?php echo e(__('Disabled')); ?></option>
                    </select>
                </div>
            </div>
            <?php if($bulk_action): ?>
                <?php echo $__env->make('include.selected-box', [ 
                    'bulk_action' => $bulk_action,
                    'datatable_list' => $moduleID.'List'
                ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php endif; ?>
            <div class="table-responsive">
                <table class="table table-striped word-wrap" data-url="<?php echo e(route('productCategoryList')); ?>" data-delete="<?php echo e(route('productCategoryDelete')); ?>" data-status="<?php echo e(route('productCategoryUpdate')); ?>">
                    <thead>
                        <tr>
                            <?php if($bulk_action): ?>
                            <th width="40px">
                                <label class="custom-checkbox">
                                    <input type="checkbox" value="1" class="form-check-input checkall" data-datatable="<?php echo e($moduleID); ?>List">
                                    <span></span>
                                </label>
                            </th>
                            <?php endif; ?>
                            <th style="min-width:150px" data-column="name" class="sortable"><?php echo e(__('Name')); ?></th>
                            <th style="min-width:100px" data-column="name" class="sortable"><?php echo e(__('Online Order')); ?></th>
                            <th style="min-width:100px" class="text-center"><?php echo e(__('Enabled')); ?></th>
                            <th style="min-width:120px" data-column="created_at" class="sortable"><?php echo e(__('Created On')); ?></th>
                            <?php if(checkPermission('edit_product_category') || checkPermission('delete_product_category')): ?>
                            <th width="100px" class="text-center"><?php echo e(__('Action')); ?></th>
                            <?php endif; ?>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
            <input type="hidden" name="sort_col" id="sort_col" value="<?php echo e(request()->sort_col); ?>">
            <input type="hidden" name="sort_by" id="sort_by" value="<?php echo e(request()->sort_by); ?>">
        </div>
    </div>

    <?php echo $__env->make('modals.layout', [
        'moduleID' => $moduleID,
        'filename' => 'product-category-add',
        'title' => __('Product Category'),
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <script type="text/javascript">
        $(document).ready(function() {
            var moduleID = "<?php echo e($moduleID); ?>";
            var listingID = '#'+moduleID+'List';
            var tableID = listingID+" table";
            var formID = '#'+moduleID+'Form';

            $(tableID).on("click", ".edit", function (event)
            {
                $(formID + " #editName").val($(this).data("name"));
                $(formID + " #editStatus").val($(this).data("status"));
                $(formID + " #editOnlineOrder").prop("checked", $(this).data("online_order") == 1);
            });
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('include.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\MBB_Backend\resources\views/product-category.blade.php ENDPATH**/ ?>