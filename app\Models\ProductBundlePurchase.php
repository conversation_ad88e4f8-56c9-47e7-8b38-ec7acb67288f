<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Http\Controllers\Admin\ProductBundleController;

class ProductBundlePurchase extends Model
{
    use HasFactory;

    protected $appends = ['membership_tiers'];


    public function discount_packages()
    {
        return $this->belongsTo(ProductBundleDiscounts::class, 'product_bundle_variant_id');
    }

    public function product()
    {
        return $this->belongsTo(Products::class, 'product_id');
    }

    public function getVariationDataAttribute()
    {
        return (new ProductBundleController)->getVariationData($this->product_variation_id);
    }
}
