<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use App\Http\Controllers\Admin\ProductsController;

class Products extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;

    public $table = "products";

    public $fillable = [
        'product_category_id',
        'product_subcategory_id'
    ];

    public $appends = [
        'image_url',
        'single_image_url',
        'in_cart'
    ];

    protected static function booted(): void
    {
        static::deleted(function (Products $product): void {
            $product->variations()->each(fn(ProductVariations $item) => $item->delete());
            $product->cartProducts()->delete();
        });

        static::updated(function (Products $product): void {
            if ($product->is_active == 0 || $product->is_out_of_stock == 1) {
                $product->cartProducts()->delete();
            }
        });
    }


    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('preview')
            ->nonQueued();
    }

    public function priceGroup()
    {
        return $this->hasOne(PriceGroups::class, 'id', 'price_group');
    }

    public function cartProducts()
    {
        return $this->hasMany(CartProducts::class, 'product_id', 'id');
    }

    public function variations()
    {
        return $this->hasMany(ProductVariations::class, 'product_id', 'id');
    }

    public function bundles()
    {
        return $this->hasMany(ProductBundle::class, 'product_id', 'id')->where('is_active', 1);
    }

    public function bundle_with_normal()
    {
        return $this->hasOne(ProductBundle::class, 'product_id', 'id')
            ->where('is_active', 1)
            ->where('type', 'normal');
    }

    public function bundle_with_quantity_discount()
    {
        return $this->hasOne(ProductBundle::class, 'product_id', 'id')
            ->where('is_active', 1)
            ->where('type', 'quantity_discount');
    }

    public function productPrices()
    {
        return $this->hasMany(ProductPrices::class, 'product_id', 'id');
    }

    public function productAttributes()
    {
        return $this->hasMany(ProductAttributes::class, 'product_id', 'id');
    }

    public function productCategory()
    {
        return $this->belongsTo(ProductCategory::class, 'product_category_id', 'id');
    }

    public function wishlists()
    {
        return $this->hasMany(UserWishlist::class, 'product_id', 'id');
    }

    public function productSubcategory()
    {
        return $this->belongsTo(ProductSubcategory::class, 'product_subcategory_id', 'id');
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', 1);
    }

    public function getImageUrlAttribute()
    {
        return $this->getMedia('product')->map(function ($image) {
            return $image->getUrl();
        });
    }

    public function getSingleImageUrlAttribute()
    {
        $images = $this->getMedia('product')->map(function ($image) {
            return $image->getUrl();
        });

        return $images[0] ?? null;
    }

    public function getInCartAttribute()
    {
        if (is_null(auth()->user())) {
            return null;
        }

        $cart = Carts::where('user_id', auth()->user()->id)->first();

        if ($cart) {
            $cartItem = $cart->products()->where('product_id', $this->id)->first();

            if ($cartItem) {
                return [
                    'cart_product_id' => $cartItem->id,
                    'quantity' => $cartItem->quantity
                ];
            }
        }

        return null;
    }
}
